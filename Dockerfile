FROM php:8.1-apache

# Install system dependencies
RUN apt-get update && apt-get install -y \
    libpng-dev \
    libjpeg-dev \
    libfreetype6-dev \
    libzip-dev \
    unzip \
    && docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install -j$(nproc) gd pdo pdo_mysql zip

# Enable Apache modules
RUN a2enmod rewrite headers

# Set working directory
WORKDIR /var/www/html

# Copy application files
COPY . .

# Set proper permissions
RUN chown -R www-data:www-data /var/www/html \
    && chmod -R 755 /var/www/html

# Configure Apache for Cloud Run
RUN echo 'ServerName localhost' >> /etc/apache2/apache2.conf

# Configure Apache for PWA
RUN echo '<Directory /var/www/html>' >> /etc/apache2/apache2.conf \
    && echo '    Options Indexes FollowSymLinks' >> /etc/apache2/apache2.conf \
    && echo '    AllowOverride All' >> /etc/apache2/apache2.conf \
    && echo '    Require all granted' >> /etc/apache2/apache2.conf \
    && echo '</Directory>' >> /etc/apache2/apache2.conf

# Add PWA-specific headers
RUN echo 'Header always set X-Content-Type-Options nosniff' >> /etc/apache2/apache2.conf \
    && echo 'Header always set X-Frame-Options DENY' >> /etc/apache2/apache2.conf \
    && echo 'Header always set X-XSS-Protection "1; mode=block"' >> /etc/apache2/apache2.conf

# Add health check configuration
RUN echo '<Location "/healthz">' >> /etc/apache2/apache2.conf \
    && echo '    Require all granted' >> /etc/apache2/apache2.conf \
    && echo '    Header always unset X-Frame-Options' >> /etc/apache2/apache2.conf \
    && echo '</Location>' >> /etc/apache2/apache2.conf

# Create Apache configuration template
RUN cp /etc/apache2/sites-available/000-default.conf /etc/apache2/sites-available/000-default.conf.template

# Create startup script for Cloud Run
COPY <<EOF /docker-entrypoint.sh
#!/bin/bash
set -e

# Get the port from environment variable (Cloud Run sets this to 80)
PORT=\${PORT:-80}

echo "Configuring Apache for port \$PORT"

# Update ports.conf
sed "s/Listen 80/Listen \$PORT/g" /etc/apache2/ports.conf > /tmp/ports.conf
mv /tmp/ports.conf /etc/apache2/ports.conf

# Update virtual host
sed "s/:80/:\$PORT/g" /etc/apache2/sites-available/000-default.conf.template > /etc/apache2/sites-available/000-default.conf

# Set Apache environment variables
export APACHE_RUN_USER=www-data
export APACHE_RUN_GROUP=www-data
export APACHE_LOG_DIR=/var/log/apache2
export APACHE_LOCK_DIR=/var/lock/apache2
export APACHE_PID_FILE=/var/run/apache2.pid
export APACHE_RUN_DIR=/var/run/apache2

# Create required directories
mkdir -p /var/run/apache2
mkdir -p /var/lock/apache2
mkdir -p /var/log/apache2

echo "Starting Apache on port \$PORT"

# Start Apache in foreground
exec apache2 -DFOREGROUND
EOF

RUN chmod +x /docker-entrypoint.sh

# Expose port (Cloud Run will override this)
EXPOSE 8080

# Use the entrypoint script
ENTRYPOINT ["/docker-entrypoint.sh"]
