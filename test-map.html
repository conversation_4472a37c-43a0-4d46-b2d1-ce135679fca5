<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Map Test</title>
    
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
          integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
          crossorigin=""/>
    
    <style>
        #testMap {
            height: 400px;
            width: 100%;
            border: 2px solid #007bff;
            margin: 20px 0;
        }
        
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>Map Loading Test</h1>
    
    <div id="status" class="status">Initializing...</div>
    
    <div id="testMap"></div>
    
    <div id="console-output">
        <h3>Console Output:</h3>
        <div id="log"></div>
    </div>

    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
            integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
            crossorigin=""></script>

    <script>
        // Override console.log to display in page
        const originalLog = console.log;
        const originalError = console.error;
        const logDiv = document.getElementById('log');
        
        function addLogEntry(message, type = 'log') {
            const entry = document.createElement('div');
            entry.style.padding = '5px';
            entry.style.margin = '2px 0';
            entry.style.backgroundColor = type === 'error' ? '#f8d7da' : '#e2e3e5';
            entry.style.borderRadius = '3px';
            entry.textContent = `[${type.toUpperCase()}] ${message}`;
            logDiv.appendChild(entry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addLogEntry(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addLogEntry(args.join(' '), 'error');
        };

        document.addEventListener('DOMContentLoaded', function() {
            const statusDiv = document.getElementById('status');
            
            try {
                console.log('DOM loaded');
                
                // Check if Leaflet is available
                if (typeof L === 'undefined') {
                    throw new Error('Leaflet library not loaded');
                }
                console.log('Leaflet library loaded successfully');
                
                // Check map container
                const mapContainer = document.getElementById('testMap');
                if (!mapContainer) {
                    throw new Error('Map container not found');
                }
                console.log('Map container found');
                console.log('Container dimensions:', mapContainer.offsetWidth, 'x', mapContainer.offsetHeight);
                
                // Create map
                console.log('Creating map...');
                const map = L.map('testMap').setView([3.8480, 11.5021], 12);
                console.log('Map created');
                
                // Add tile layer
                console.log('Adding tile layer...');
                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
                    maxZoom: 18
                }).addTo(map);
                console.log('Tile layer added');
                
                // Add marker
                console.log('Adding marker...');
                L.marker([3.8480, 11.5021])
                    .addTo(map)
                    .bindPopup('ICT University, Yaoundé')
                    .openPopup();
                console.log('Marker added');
                
                statusDiv.className = 'status success';
                statusDiv.textContent = 'Map loaded successfully!';
                console.log('Map initialization completed');
                
            } catch (error) {
                console.error('Error:', error.message);
                statusDiv.className = 'status error';
                statusDiv.textContent = 'Error: ' + error.message;
            }
        });
    </script>
</body>
</html>
